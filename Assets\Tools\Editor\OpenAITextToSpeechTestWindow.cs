using UnityEditor;
using UnityEngine;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for testing OpenAI Text-to-Speech functionality.
    /// </summary>
    public class OpenAITextToSpeechTestWindow : BaseDebugWindow
    {
        #region Private Fields
        private string apiKey = "";
        private string apiUrl = "https://api.openai.com/v1/audio/speech";
        private string model = "tts-1";
        private string inputText = "Hello, this is a test.";
        private string voice = "alloy";
        private string instructions = "";
        private float speed = 1.0f;
        
        private AudioSource previewAudioSource;
        private bool isGenerating = false;
        private string lastError = "";
        #endregion

        #region Window Management
        /// <summary>
        /// Shows the OpenAI TTS Test window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<OpenAITextToSpeechTestWindow>("OpenAI TTS Tester");
            window.minSize = new Vector2(400, 500);
            window.Show();
        }
        #endregion

        #region Unity Editor Methods
        protected override void OnEnable()
        {
            base.OnEnable();
            
            // Create a temporary GameObject for audio preview if it doesn't exist
            if (previewAudioSource == null)
            {
                var tempGO = new GameObject("TTS Preview Audio Source");
                tempGO.hideFlags = HideFlags.HideAndDontSave;
                previewAudioSource = tempGO.AddComponent<AudioSource>();
            }
        }

        private void OnDisable()
        {
            // Clean up the temporary audio source
            if (previewAudioSource != null)
            {
                DestroyImmediate(previewAudioSource.gameObject);
                previewAudioSource = null;
            }
        }

        private void OnGUI()
        {
            InitializeStyles();
            DrawHeader("OpenAI Text-to-Speech Tester", "Refresh Settings", "Clear Results");
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            DrawAPISettings();
            EditorGUILayout.Space(10);
            DrawTTSSettings();
            EditorGUILayout.Space(10);
            DrawControls();
            EditorGUILayout.Space(10);
            DrawStatus();
            
            EditorGUILayout.EndScrollView();
        }
        #endregion

        #region UI Drawing Methods
        private void DrawAPISettings()
        {
            EditorGUILayout.LabelField("API Settings", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            apiKey = EditorGUILayout.PasswordField("API Key", apiKey);
            apiUrl = EditorGUILayout.TextField("API URL", apiUrl);
            model = EditorGUILayout.TextField("Model", model);
            
            EditorGUILayout.EndVertical();
        }

        private void DrawTTSSettings()
        {
            EditorGUILayout.LabelField("TTS Settings", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            inputText = EditorGUILayout.TextArea(inputText, GUILayout.Height(60));
            
            // Voice selection dropdown
            string[] voiceOptions = { "alloy", "echo", "fable", "onyx", "nova", "shimmer" };
            int voiceIndex = System.Array.IndexOf(voiceOptions, voice);
            if (voiceIndex == -1) voiceIndex = 0;
            voiceIndex = EditorGUILayout.Popup("Voice", voiceIndex, voiceOptions);
            voice = voiceOptions[voiceIndex];
            
            instructions = EditorGUILayout.TextField("Instructions", instructions);
            speed = EditorGUILayout.Slider("Speed", speed, 0.25f, 4.0f);
            
            EditorGUILayout.EndVertical();
        }

        private void DrawControls()
        {
            EditorGUILayout.LabelField("Controls", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            EditorGUI.BeginDisabledGroup(isGenerating || string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(inputText));
            if (GUILayout.Button(isGenerating ? "Generating..." : "Generate and Play TTS", buttonStyle, GUILayout.Height(30)))
            {
                GenerateAndPlayTTS();
            }
            EditorGUI.EndDisabledGroup();
            
            if (previewAudioSource != null && previewAudioSource.clip != null)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Play", GUILayout.Width(60)))
                {
                    previewAudioSource.Play();
                }
                if (GUILayout.Button("Stop", GUILayout.Width(60)))
                {
                    previewAudioSource.Stop();
                }
                EditorGUILayout.LabelField($"Clip: {previewAudioSource.clip.name} ({previewAudioSource.clip.length:F1}s)");
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawStatus()
        {
            EditorGUILayout.LabelField("Status", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            if (isGenerating)
            {
                EditorGUILayout.HelpBox("Generating TTS audio...", MessageType.Info);
            }
            else if (!string.IsNullOrEmpty(lastError))
            {
                EditorGUILayout.HelpBox($"Error: {lastError}", MessageType.Error);
            }
            else if (previewAudioSource != null && previewAudioSource.clip != null)
            {
                EditorGUILayout.HelpBox("TTS audio generated successfully!", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }
        #endregion

        #region TTS Generation
        private async void GenerateAndPlayTTS()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                lastError = "Please set your OpenAI API key!";
                return;
            }

            isGenerating = true;
            lastError = "";
            Repaint();

            try
            {
                var ttsRequest = new SmartVertex.Tools.OpenAITextToSpeech.TTSRequest
                {
                    model = model,
                    input = inputText,
                    voice = voice,
                    instructions = instructions,
                    response_format = "mp3",
                    speed = speed
                };

                var ttsService = new SmartVertex.Tools.OpenAITextToSpeech(ttsRequest, apiKey, apiUrl);
                AudioClip clip = await ttsService.Generate();
                
                if (clip != null)
                {
                    previewAudioSource.clip = clip;
                    previewAudioSource.Play();
                    lastError = "";
                    Debug.Log("TTS audio generated and played successfully.");
                }
                else
                {
                    lastError = "Failed to generate TTS audio.";
                    Debug.LogError(lastError);
                }
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
                Debug.LogError($"TTS Generation Error: {ex.Message}");
            }
            finally
            {
                isGenerating = false;
                Repaint();
            }
        }
        #endregion

        #region BaseDebugWindow Overrides
        protected override void OnRefreshClicked()
        {
            lastError = "";
            Repaint();
        }

        protected override void OnClearClicked()
        {
            if (previewAudioSource != null)
            {
                previewAudioSource.Stop();
                previewAudioSource.clip = null;
            }
            lastError = "";
            Repaint();
        }
        #endregion
    }
}
