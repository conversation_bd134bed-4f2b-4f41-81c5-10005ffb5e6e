using UnityEditor;
using UnityEngine;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Editor window for testing Gemini API functionality.
    /// </summary>
    public class GeminiAPITestWindow : BaseDebugWindow
    {
        #region Private Fields
        private string apiKey = "";
        private string model = "gemini-2.5-flash";
        private string inputText = "Explain how AI works in simple terms.";
        private string systemInstruction = "You are a helpful AI assistant.";
        
        private float temperature = 1.0f;
        private float topP = 0.95f;
        private int topK = 40;
        private int thinkingBudget = 0;
        
        private bool isGenerating = false;
        private string lastResponse = "";
        private string lastError = "";
        private string lastUsageInfo = "";
        
        private Vector2 responseScrollPosition;
        #endregion

        #region Window Management
        /// <summary>
        /// Shows the Gemini API Test window.
        /// </summary>
        public static void ShowWindow()
        {
            var window = GetWindow<GeminiAPITestWindow>("Gemini API Tester");
            window.minSize = new Vector2(500, 600);
            window.Show();
        }
        #endregion

        #region Unity Editor Methods
        private void OnGUI()
        {
            InitializeStyles();
            DrawHeader("Gemini API Tester", "Refresh Settings", "Clear Results");
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            DrawAPISettings();
            EditorGUILayout.Space(10);
            DrawGenerationSettings();
            EditorGUILayout.Space(10);
            DrawInputSettings();
            EditorGUILayout.Space(10);
            DrawControls();
            EditorGUILayout.Space(10);
            DrawResponse();
            EditorGUILayout.Space(10);
            DrawStatus();
            
            EditorGUILayout.EndScrollView();
        }
        #endregion

        #region UI Drawing Methods
        private void DrawAPISettings()
        {
            EditorGUILayout.LabelField("API Settings", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            apiKey = EditorGUILayout.PasswordField("API Key", apiKey);
            model = EditorGUILayout.TextField("Model", model);
            
            EditorGUILayout.EndVertical();
        }

        private void DrawGenerationSettings()
        {
            EditorGUILayout.LabelField("Generation Configuration", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            temperature = EditorGUILayout.Slider("Temperature", temperature, 0.0f, 2.0f);
            topP = EditorGUILayout.Slider("Top P", topP, 0.0f, 1.0f);
            topK = EditorGUILayout.IntSlider("Top K", topK, 1, 100);
            
            EditorGUILayout.LabelField("Thinking Budget (0=off, -1=dynamic, >0=specific)");
            thinkingBudget = EditorGUILayout.IntField("Thinking Budget", thinkingBudget);
            
            EditorGUILayout.EndVertical();
        }

        private void DrawInputSettings()
        {
            EditorGUILayout.LabelField("Input Settings", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            EditorGUILayout.LabelField("System Instruction:");
            systemInstruction = EditorGUILayout.TextArea(systemInstruction, GUILayout.Height(40));
            
            EditorGUILayout.LabelField("Input Text:");
            inputText = EditorGUILayout.TextArea(inputText, GUILayout.Height(60));
            
            EditorGUILayout.EndVertical();
        }

        private void DrawControls()
        {
            EditorGUILayout.LabelField("Controls", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            EditorGUI.BeginDisabledGroup(isGenerating || string.IsNullOrEmpty(apiKey));
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button(isGenerating ? "Generating..." : "Generate Content", buttonStyle))
            {
                GenerateContent();
            }
            if (GUILayout.Button("Generate Simple Text", buttonStyle))
            {
                GenerateSimpleText();
            }
            EditorGUILayout.EndHorizontal();
            
            if (GUILayout.Button("Generate with Thinking", buttonStyle))
            {
                GenerateWithThinking();
            }
            
            EditorGUI.EndDisabledGroup();
            
            EditorGUILayout.EndVertical();
        }

        private void DrawResponse()
        {
            EditorGUILayout.LabelField("Response", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            if (!string.IsNullOrEmpty(lastResponse))
            {
                responseScrollPosition = EditorGUILayout.BeginScrollView(responseScrollPosition, GUILayout.Height(200));
                EditorGUILayout.SelectableLabel(lastResponse, EditorStyles.textArea, GUILayout.ExpandHeight(true));
                EditorGUILayout.EndScrollView();
                
                if (!string.IsNullOrEmpty(lastUsageInfo))
                {
                    EditorGUILayout.Space(5);
                    EditorGUILayout.LabelField("Usage Information:", EditorStyles.boldLabel);
                    EditorGUILayout.SelectableLabel(lastUsageInfo, EditorStyles.miniLabel);
                }
            }
            else
            {
                EditorGUILayout.LabelField("No response yet. Click a generate button to test the API.", EditorStyles.centeredGreyMiniLabel);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawStatus()
        {
            EditorGUILayout.LabelField("Status", subHeaderStyle);
            EditorGUILayout.BeginVertical(boxStyle);
            
            if (isGenerating)
            {
                EditorGUILayout.HelpBox("Generating content...", MessageType.Info);
            }
            else if (!string.IsNullOrEmpty(lastError))
            {
                EditorGUILayout.HelpBox($"Error: {lastError}", MessageType.Error);
            }
            else if (!string.IsNullOrEmpty(lastResponse))
            {
                EditorGUILayout.HelpBox("Content generated successfully!", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }
        #endregion

        #region Content Generation Methods
        private async void GenerateContent()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                lastError = "Please set your Gemini API key!";
                return;
            }

            isGenerating = true;
            lastError = "";
            Repaint();

            try
            {
                var request = new SmartVertex.Tools.GeminiAPI.GeminiRequest
                {
                    system_instruction = new SmartVertex.Tools.GeminiAPI.SystemInstruction
                    {
                        parts = new SmartVertex.Tools.GeminiAPI.Part[]
                        {
                            new SmartVertex.Tools.GeminiAPI.Part { text = systemInstruction }
                        }
                    },
                    contents = new SmartVertex.Tools.GeminiAPI.Content[]
                    {
                        new SmartVertex.Tools.GeminiAPI.Content
                        {
                            parts = new SmartVertex.Tools.GeminiAPI.Part[]
                            {
                                new SmartVertex.Tools.GeminiAPI.Part { text = inputText }
                            }
                        }
                    },
                    generationConfig = new SmartVertex.Tools.GeminiAPI.GenerationConfig
                    {
                        temperature = temperature,
                        topP = topP,
                        topK = topK,
                        thinkingConfig = thinkingBudget != 0 ? new SmartVertex.Tools.GeminiAPI.ThinkingConfig { thinkingBudget = thinkingBudget } : null
                    }
                };

                string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
                var geminiService = new SmartVertex.Tools.GeminiAPI(request, apiKey, apiUrl);

                var response = await geminiService.GenerateContent();
                ProcessResponse(response);
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
                Debug.LogError($"Gemini API Error: {ex.Message}");
            }
            finally
            {
                isGenerating = false;
                Repaint();
            }
        }

        private async void GenerateSimpleText()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                lastError = "Please set your Gemini API key!";
                return;
            }

            isGenerating = true;
            lastError = "";
            Repaint();

            try
            {
                var request = SmartVertex.Tools.GeminiAPI.CreateSimpleTextRequest(inputText);
                string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
                var geminiService = new SmartVertex.Tools.GeminiAPI(request, apiKey, apiUrl);

                var response = await geminiService.GenerateContent();
                ProcessResponse(response);
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
                Debug.LogError($"Gemini API Error: {ex.Message}");
            }
            finally
            {
                isGenerating = false;
                Repaint();
            }
        }

        private async void GenerateWithThinking()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                lastError = "Please set your Gemini API key!";
                return;
            }

            isGenerating = true;
            lastError = "";
            Repaint();

            try
            {
                var request = SmartVertex.Tools.GeminiAPI.CreateRequestWithConfig(
                    "Solve this complex problem: What are the key differences between machine learning and deep learning?",
                    temperature: 0.7f,
                    thinkingBudget: 1024
                );

                string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
                var geminiService = new SmartVertex.Tools.GeminiAPI(request, apiKey, apiUrl);

                var response = await geminiService.GenerateContent();
                ProcessResponse(response);
            }
            catch (System.Exception ex)
            {
                lastError = ex.Message;
                Debug.LogError($"Gemini API Error: {ex.Message}");
            }
            finally
            {
                isGenerating = false;
                Repaint();
            }
        }

        private void ProcessResponse(SmartVertex.Tools.GeminiAPI.GeminiResponse response)
        {
            if (response != null)
            {
                lastResponse = SmartVertex.Tools.GeminiAPI.ExtractTextFromResponse(response);
                lastError = "";

                // Build usage information
                var usageInfo = new System.Text.StringBuilder();
                if (response.usageMetadata != null)
                {
                    usageInfo.AppendLine($"Token Usage - Prompt: {response.usageMetadata.promptTokenCount}, " +
                                       $"Response: {response.usageMetadata.candidatesTokenCount}, " +
                                       $"Total: {response.usageMetadata.totalTokenCount}");
                }

                if (response.candidates != null && response.candidates.Length > 0)
                {
                    usageInfo.AppendLine($"Finish Reason: {response.candidates[0].finishReason}");
                }

                lastUsageInfo = usageInfo.ToString();
                Debug.Log($"Generated Content: {lastResponse}");
                Debug.Log(lastUsageInfo);
            }
            else
            {
                lastError = "Failed to generate content.";
                lastResponse = "";
                lastUsageInfo = "";
                Debug.LogError(lastError);
            }
        }
        #endregion

        #region BaseDebugWindow Overrides
        protected override void OnRefreshClicked()
        {
            lastError = "";
            Repaint();
        }

        protected override void OnClearClicked()
        {
            lastResponse = "";
            lastError = "";
            lastUsageInfo = "";
            responseScrollPosition = Vector2.zero;
            Repaint();
        }
        #endregion
    }
}
