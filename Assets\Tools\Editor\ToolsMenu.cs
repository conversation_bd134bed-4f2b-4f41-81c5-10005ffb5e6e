using UnityEditor;

namespace SmartVertex.EditorTools
{
    /// <summary>
    /// Provides menu items for SmartVertex Tools in the Unity Editor.
    /// </summary>
    public static class ToolsMenu
    {
        /// <summary>
        /// Opens the Service Locator Debugger window.
        /// </summary>
        [MenuItem("Tools/Service Locator Debugger", priority = 100)]
        public static void OpenServiceLocatorWindow()
        {
            ServiceLocatorWindow.ShowWindow();
        }

        /// <summary>
        /// Opens the Event Bus Debugger window.
        /// </summary>
        [MenuItem("Tools/Event Bus Debugger", priority = 101)]
        public static void OpenEventBusWindow()
        {
            EventBusWindow.ShowWindow();
        }

        /// <summary>
        /// Opens the Grid Utility Tester window.
        /// </summary>
        [MenuItem("Tools/Grid Utility Tester", priority = 102)]
        public static void OpenGridUtilityTestWindow()
        {
            GridUtilityTestWindow.ShowWindow();
        }

        /// <summary>
        /// Opens the OpenAI Text-to-Speech Tester window.
        /// </summary>
        [MenuItem("Tools/OpenAI TTS Tester", priority = 103)]
        public static void OpenOpenAITTSTestWindow()
        {
            OpenAITextToSpeechTestWindow.ShowWindow();
        }

        /// <summary>
        /// Opens the Gemini API Tester window.
        /// </summary>
        [MenuItem("Tools/Gemini API Tester", priority = 104)]
        public static void OpenGeminiAPITestWindow()
        {
            GeminiAPITestWindow.ShowWindow();
        }
    }
}
